package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type clientResult struct {
	ID                 string     `json:"id"`
	Name               string     `json:"name"`
	FatherName         *string    `json:"father_name"`
	MotherName         *string    `json:"mother_name"`
	ClientType         string     `json:"client_type"`
	DocumentType       string     `json:"document_type"`
	Document           string     `json:"document"`
	Ubication          *string    `json:"ubication"`
	SocialReason       *string    `json:"social_reason"`
	CommercialName     *string    `json:"commercial_name"`
	Condition          *string    `json:"condition"`
	State              *string    `json:"state"`
	HasRetentionRegime *bool      `json:"has_retention_regime"`
	BusinessLineID     *string    `json:"business_line_id"`
	SubBusinessLineID  *string    `json:"sub_business_line_id"`
	ChannelID          *string    `json:"channel_id"`
	SellerID           *string    `json:"seller_id"`
	ContactName        *string    `json:"contact_name"`
	Email              *string    `json:"email"`
	Phone              *string    `json:"phone"`
	CreatedAt          *time.Time `json:"created_at"`
	UpdatedAt          *time.Time `json:"updated_at"`
	DeletedAt          *time.Time `json:"deleted_at"`
}

func clientToResult(client model.Client) clientResult {
	return clientResult{
		ID:                 client.ID,
		Name:               client.Name,
		FatherName:         client.FatherName,
		MotherName:         client.MotherName,
		ClientType:         client.ClientType,
		DocumentType:       client.DocumentType,
		Document:           client.Document,
		Ubication:          client.Ubication,
		SocialReason:       client.SocialReason,
		CommercialName:     client.CommercialName,
		Condition:          client.Condition,
		State:              client.State,
		HasRetentionRegime: client.HasRetentionRegime,
		BusinessLineID:     client.BusinessLineID,
		SubBusinessLineID:  client.SubBusinessLineID,
		ChannelID:          client.ChannelID,
		SellerID:           client.SellerID,
		ContactName:        client.ContactName,
		Email:              client.Email,
		Phone:              client.Phone,
		CreatedAt:          client.CreatedAt,
		UpdatedAt:          client.UpdatedAt,
		DeletedAt:          client.DeletedAt,
	}
}

// GetById implements ClientHandler.
func (c *clientHandler) GetById(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	id := r.PathValue("id")

	client, err := c.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get client")
		return
	}

	rest.SuccessDResponse(w, r, clientToResult(*client), http.StatusOK)
}

// GetAll implements ClientHandler.
func (c *clientHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	clients, err := c.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get clients")
		return
	}

	var results []clientResult
	for _, client := range clients {
		results = append(results, clientToResult(client))
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}
