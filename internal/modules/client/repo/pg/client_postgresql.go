package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ClientPostgreRepo interface {
	Create(ctx context.Context, client model.Client) error
	Update(ctx context.Context, client model.Client) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Client, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.Client, error)
	Delete(ctx context.Context, id string) error
}

type clientPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewClientPostgreRepo(pool *pgxpool.Pool) ClientPostgreRepo {
	return &clientPostgreRepo{
		pool: pool,
	}
}

// Create implements ClientPostgreRepo.
func (c *clientPostgreRepo) Create(ctx context.Context, client model.Client) error {
	return pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO clients (
				id, name, father_name, mother_name, client_type, document_type, document,
				ubication, social_reason, commercial_name, condition, state, has_retention_regime,
				business_line_id, sub_business_line_id, channel_id, seller_id, contact_name, email, phone,
				created_at, updated_at
			)
			VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20,
				CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
			)
		`
		_, err := conn.Exec(ctx, query,
			client.ID, client.Name, client.FatherName, client.MotherName, client.ClientType,
			client.DocumentType, client.Document, client.Ubication, client.SocialReason,
			client.CommercialName, client.Condition, client.State, client.HasRetentionRegime,
			client.BusinessLineID, client.SubBusinessLineID, client.ChannelID, client.SellerID,
			client.ContactName, client.Email, client.Phone,
		)
		return err
	})
}

// Update implements ClientPostgreRepo.
func (c *clientPostgreRepo) Update(ctx context.Context, client model.Client) error {
	return pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE clients SET
				name = $2, father_name = $3, mother_name = $4, client_type = $5,
				document_type = $6, document = $7, ubication = $8, social_reason = $9,
				commercial_name = $10, condition = $11, state = $12, has_retention_regime = $13,
				business_line_id = $14, sub_business_line_id = $15, channel_id = $16, seller_id = $17,
				contact_name = $18, email = $19, phone = $20, updated_at = CURRENT_TIMESTAMP
			WHERE id = $1
		`
		_, err := conn.Exec(ctx, query,
			client.ID, client.Name, client.FatherName, client.MotherName, client.ClientType,
			client.DocumentType, client.Document, client.Ubication, client.SocialReason,
			client.CommercialName, client.Condition, client.State, client.HasRetentionRegime,
			client.BusinessLineID, client.SubBusinessLineID, client.ChannelID, client.SellerID,
			client.ContactName, client.Email, client.Phone,
		)
		return err
	})
}

// GetByProp implements ClientPostgreRepo.
func (c *clientPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Client, error) {
	var client model.Client
	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, father_name, mother_name, client_type, document_type, document,
				   ubication, social_reason, commercial_name, condition, state, has_retention_regime,
				   business_line_id, sub_business_line_id, channel_id, seller_id, contact_name, email, phone,
				   created_at, updated_at, deleted_at
			FROM clients
			WHERE ` + prop + ` = $1 AND deleted_at IS NULL
		`
		row := conn.QueryRow(ctx, query, value)
		return row.Scan(
			&client.ID, &client.Name, &client.FatherName, &client.MotherName, &client.ClientType,
			&client.DocumentType, &client.Document, &client.Ubication, &client.SocialReason,
			&client.CommercialName, &client.Condition, &client.State, &client.HasRetentionRegime,
			&client.BusinessLineID, &client.SubBusinessLineID, &client.ChannelID, &client.SellerID,
			&client.ContactName, &client.Email, &client.Phone,
			&client.CreatedAt, &client.UpdatedAt, &client.DeletedAt,
		)
	})
	if err != nil {
		return nil, err
	}
	return &client, nil
}

// CountByProp implements ClientPostgreRepo.
func (c *clientPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `SELECT COUNT(*) FROM clients WHERE ` + prop + ` = $1 AND deleted_at IS NULL`
		row := conn.QueryRow(ctx, query, value)
		return row.Scan(&count)
	})
	return count, err
}

// GetAll implements ClientPostgreRepo.
func (c *clientPostgreRepo) GetAll(ctx context.Context) ([]model.Client, error) {
	var clients []model.Client
	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, father_name, mother_name, client_type, document_type, document,
				   ubication, social_reason, commercial_name, condition, state, has_retention_regime,
				   business_line_id, sub_business_line_id, channel_id, seller_id, contact_name, email, phone,
				   created_at, updated_at, deleted_at
			FROM clients
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`
		rows, err := conn.Query(ctx, query)
		if err != nil {
			return err
		}
		defer rows.Close()

		for rows.Next() {
			var client model.Client
			err := rows.Scan(
				&client.ID, &client.Name, &client.FatherName, &client.MotherName, &client.ClientType,
				&client.DocumentType, &client.Document, &client.Ubication, &client.SocialReason,
				&client.CommercialName, &client.Condition, &client.State, &client.HasRetentionRegime,
				&client.BusinessLineID, &client.SubBusinessLineID, &client.ChannelID, &client.SellerID,
				&client.ContactName, &client.Email, &client.Phone,
				&client.CreatedAt, &client.UpdatedAt, &client.DeletedAt,
			)
			if err != nil {
				return err
			}
			clients = append(clients, client)
		}
		return rows.Err()
	})
	return clients, err
}

// Delete implements ClientPostgreRepo.
func (c *clientPostgreRepo) Delete(ctx context.Context, id string) error {
	return pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `UPDATE clients SET deleted_at = CURRENT_TIMESTAMP WHERE id = $1`
		_, err := conn.Exec(ctx, query, id)
		return err
	})
}
